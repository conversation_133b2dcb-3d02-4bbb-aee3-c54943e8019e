// This script ensures that for all deployed proxy  contracts
// the admin address is 0x00
// the implementation is the expected implementation to detect front running

import { ethers } from "hardhat";

interface ContractInfo {
  name: string;
  proxy: string;
  expectedImplementation: string;
}

async function main() {
  // ERC1967 storage slots
  const implSlot = "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc";
  const adminSlot = "0xb53127684a568b3173ae13b9f8a6016e01a42b6a20e6f9e3c2ee9f35ab414d55";
  const zeroAddress = "******************************************";

  // All your deployed contracts with their expected implementations
  const contracts: ContractInfo[] = [
    {
      name: "StorageToken",
      proxy: "******************************************",
      expectedImplementation: "******************************************"
    },
    {
      name: "TokenDistributionEngine",
      proxy: "******************************************",
      expectedImplementation: "******************************************"
    },
    {
      name: "AirdropContract",
      proxy: "******************************************",
      expectedImplementation: "0x0E870C8c51e9B457C40fFb3ad22AeD1f30fD0088"
    },
    {
      name: "TestnetMiningRewards",
      proxy: "0x1Def7229f6d6Ca5fbA4f9e28Cd1cf4e2688e545d",
      expectedImplementation: "0xdc1bB05397CAC751fA353bb39805A6B16cB08119"
    },
    {
      name: "StakingEngineLinear",
      proxy: "0x32A2b049b1E7A6c8C26284DE49e7F05A00466a5d",
      expectedImplementation: "0xC31db852C347322440f9027A5D65d8FD39B18C46"
    },
    {
      name: "StakePool (StakingLinear)",
      proxy: "0xfa9cb36656cf9A2D2BA3a6b0aD810fB9993F7A21",
      expectedImplementation: "0x860793bb966511d8Ddfb03a0598A1E5b3a83225f"
    },
    {
      name: "RewardPool (StakingLinear)",
      proxy: "0xDB2ab8De23eb8dd6cd12127673be9ae6Ae6edd9A",
      expectedImplementation: "0x860793bb966511d8Ddfb03a0598A1E5b3a83225f"
    },
    {
      name: "StakingPool (StoragePool)",
      proxy: "0x4bE8054fF2De1353dAE37518a8C95D0a314c704E",
      expectedImplementation: "0xa3FB8C0c90F54B167c463E9927E69a7D5b8eD4be"
    },
    {
      name: "StoragePool",
      proxy: "0xFe348d762851497c4E4Cd0cA39386c070e213FD7",
      expectedImplementation: "0x24FC7aF513f60E752C65e4Bc8135c0e6afD03b04"
    },
    {
      name: "StakingPool (RewardEngine)",
      proxy: "0x2Fe7e91D48C243958a9efd76ADd2793ff52a69B0",
      expectedImplementation: "0xa3FB8C0c90F54B167c463E9927E69a7D5b8eD4be"
    },
    {
      name: "RewardEngine",
      proxy: "******************************************",
      expectedImplementation: "******************************************"
    }
  ];

  console.log("🔍 Checking proxy storage for all contracts...\n");

  let hasInconsistencies = false;

  for (const contract of contracts) {
    console.log(`📋 Checking ${contract.name}:`);
    console.log(`   Proxy: ${contract.proxy}`);

    try {
      // Get implementation address from storage
      const implRaw = await ethers.provider.send("eth_getStorageAt", [
        contract.proxy,
        implSlot,
        "latest",
      ]);

      // Get admin address from storage
      const adminRaw = await ethers.provider.send("eth_getStorageAt", [
        contract.proxy,
        adminSlot,
        "latest",
      ]);

      const actualImpl = ethers.getAddress("0x" + implRaw.slice(26));
      const actualAdmin = ethers.getAddress("0x" + adminRaw.slice(26));

      // Check implementation
      const implMatch = actualImpl.toLowerCase() === contract.expectedImplementation.toLowerCase();
      console.log(`   Implementation: ${actualImpl} ${implMatch ? "✅" : "❌"}`);
      if (!implMatch) {
        console.log(`   Expected: ${contract.expectedImplementation}`);
        hasInconsistencies = true;
      }

      // Check admin (should be zero address)
      const adminIsZero = actualAdmin.toLowerCase() === zeroAddress.toLowerCase();
      console.log(`   Admin: ${actualAdmin} ${adminIsZero ? "✅" : "❌"}`);
      if (!adminIsZero) {
        console.log(`   Expected: ${zeroAddress} (zero address)`);
        hasInconsistencies = true;
      }

    } catch (error) {
      console.log(`   ❌ Error checking contract: ${error}`);
      hasInconsistencies = true;
    }

    console.log(); // Empty line for readability
  }

  // Summary
  console.log("=" .repeat(60));
  if (hasInconsistencies) {
    console.log("❌ INCONSISTENCIES FOUND! Please review the issues above.");
    process.exit(1);
  } else {
    console.log("✅ ALL CONTRACTS VERIFIED SUCCESSFULLY!");
    console.log("   - All implementations match expected addresses");
    console.log("   - All admin addresses are set to zero address");
  }
}

main().catch((err) => {
  console.error("❌ Script failed:", err);
  process.exit(1);
});

// npx hardhat run scripts/checkProxyStorage.ts --network base