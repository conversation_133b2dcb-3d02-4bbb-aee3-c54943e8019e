import { ethers } from "hardhat";

async function main() {
  const proxy = "******************************************";
  const implSlot =
    "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc";
  const adminSlot =
    "0xb53127684a568b3173ae13b9f8a6016e01a42b6a20e6f9e3c2ee9f35ab414d55";

  const implRaw = await ethers.provider.send("eth_getStorageAt", [
    proxy,
    implSlot,
    "latest",
  ]);
  const adminRaw = await ethers.provider.send("eth_getStorageAt", [
    proxy,
    adminSlot,
    "latest",
  ]);

  const impl = ethers.getAddress("0x" + implRaw.slice(26));
  const admin = ethers.getAddress("0x" + adminRaw.slice(26));

  console.log("Implementation address:", impl);
  console.log("Admin address:", admin);
}

main().catch((err) => {
  console.error(err);
  process.exit(1);
});